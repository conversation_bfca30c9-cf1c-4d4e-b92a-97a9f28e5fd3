#!/bin/bash

methods=("all_at_once" "step_by_step" "binary_search")
model="gpt-4o"

# Run the experiment 4 times
for run_num in {1..4}; do
    echo "🚀 Starting run $run_num/3..."

    # Create output directory for this run
    mkdir -p "outputs/with_gt/$run_num"

    for method in "${methods[@]}"; do
        echo "🔄 Running $method (run $run_num)..."

        # Handcrafted
        # python inference_cloudgpt_with_gt.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted True --directory_path "../Who&When/Hand-Crafted" --output_dir "outputs/with_gt/$run_num"
        # python evaluate_v2.py --eval_file "outputs/$run_num/${method}_${model}_handcrafted.txt" --data_path "../Who&When/Hand-Crafted"

        # Algorithm-generated
        python inference_cloudgpt_with_gt.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted False --directory_path "../Who&When/Algorithm-Generated" --output_dir "outputs/with_gt/$run_num"
        # python evaluate_v2.py --eval_file "outputs/$run_num/${method}_${model}_algorithm-generated.txt" --data_path "../Who&When/Algorithm-Generated"
    done

    echo "✅ Run $run_num completed!"
done

echo "🎉 All 3 runs completed!"

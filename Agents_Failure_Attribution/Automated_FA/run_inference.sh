#!/bin/bash

# Configuration
method="your_method"  # Replace with your method
model="your_model"    # Replace with your model
run_num="1"           # Replace with your run number

# Azure OpenAI configuration
azure_endpoint="https://cloudgpt-openai.azure-api.net/"
api_version="2025-04-01-preview"

# Directory paths
handcrafted_dir="../Who&When/Hand-Crafted"
output_base_dir="outputs"

echo "Starting inference runs..."

# Run inference with ground truth
echo "Running inference_cloudgpt_with_gt.py..."
python inference_cloudgpt_with_gt.py \
    --method $method \
    --model $model \
    --azure_endpoint $azure_endpoint \
    --api_version $api_version \
    --is_handcrafted True \
    --directory_path $handcrafted_dir \
    --output_dir "$output_base_dir/with_gt/$run_num"

echo "Completed inference_cloudgpt_with_gt.py"

# Run inference without ground truth
echo "Running inference_cloudgpt_without_gt.py..."
python inference_cloudgpt_without_gt.py \
    --method $method \
    --model $model \
    --azure_endpoint $azure_endpoint \
    --api_version $api_version \
    --is_handcrafted True \
    --directory_path $handcrafted_dir \
    --output_dir "$output_base_dir/without_gt/$run_num"

echo "Completed inference_cloudgpt_without_gt.py"
echo "All inference runs completed!"

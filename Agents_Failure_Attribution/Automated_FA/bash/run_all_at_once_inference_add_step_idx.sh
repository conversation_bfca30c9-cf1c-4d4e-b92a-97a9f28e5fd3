#!/bin/bash

model="gpt-4o"
method="all_at_once"
prompt_template="add_step_idx"

# Run the experiment 4 times
for run_num in {1..4}; do
    echo "🚀 Starting run $run_num/4 (All-at-Once with add_step_idx)..."

    # Create output directories for this run
    mkdir -p "../outputs_new/AAO/add_step_idx/with_gt/$run_num"
    mkdir -p "../outputs_new/AAO/add_step_idx/without_gt/$run_num"

    echo "🔄 Running $method with $prompt_template template (run $run_num)..."

    # Run inference WITH ground truth
    echo "  📊 Running with_gt inference..."
    
    # Handcrafted data
    python ../inference_cloudgpt_with_gt.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted True --directory_path "../../Who&When/Hand-Crafted" --output_dir "../outputs_new/AAO/add_step_idx/with_gt/$run_num" --prompt_template $prompt_template
    
    # Algorithm-generated data
    python ../inference_cloudgpt_with_gt.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted False --directory_path "../../Who&When/Algorithm-Generated" --output_dir "../outputs_new/AAO/add_step_idx/with_gt/$run_num" --prompt_template $prompt_template

    # Run inference WITHOUT ground truth
    echo "  📊 Running without_gt inference..."
    
    # Handcrafted data
    python ../inference_cloudgpt_without_gt.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted True --directory_path "../../Who&When/Hand-Crafted" --output_dir "../outputs_new/AAO/add_step_idx/without_gt/$run_num" --prompt_template $prompt_template
    
    # Algorithm-generated data
    python ../inference_cloudgpt_without_gt.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted False --directory_path "../../Who&When/Algorithm-Generated" --output_dir "../outputs_new/AAO/add_step_idx/without_gt/$run_num" --prompt_template $prompt_template

    echo "✅ Run $run_num completed!"
done

echo "🎉 All 4 runs completed for All-at-Once with add_step_idx template!"
echo "📁 Results saved to: outputs_new/AAO/add_step_idx/"
